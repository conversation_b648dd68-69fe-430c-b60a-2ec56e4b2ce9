<?php
/**
 * Debug API endpoints directly
 */

echo "Testing API endpoints...\n\n";

// Test projects.php GET request
echo "1. Testing projects.php GET request:\n";
echo "URL: http://localhost/erdevwe/api/projects.php\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/erdevwe/api/projects.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $headerSize);
$body = substr($response, $headerSize);

echo "HTTP Code: $httpCode\n";
echo "Headers:\n$headers\n";
echo "Body:\n$body\n\n";

curl_close($ch);

// Test media.php GET request
echo "2. Testing media.php GET request:\n";
echo "URL: http://localhost/erdevwe/api/media.php\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/erdevwe/api/media.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $headerSize);
$body = substr($response, $headerSize);

echo "HTTP Code: $httpCode\n";
echo "Headers:\n$headers\n";
echo "Body:\n$body\n\n";

curl_close($ch);

echo "API testing complete.\n";
?>

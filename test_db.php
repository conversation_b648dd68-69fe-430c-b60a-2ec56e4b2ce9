<?php
/**
 * Test database connection and check tables
 */

require_once 'config/config.php';

echo "Testing database connection...\n";

try {
    // Test basic connection
    $pdo = $db->getConnection();
    echo "✓ Database connection successful\n";
    
    // Check if database exists and show tables
    $tables = $db->fetchAll("SHOW TABLES");
    echo "✓ Found " . count($tables) . " tables:\n";
    
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "  - $tableName\n";
        
        // Check if it's one of our main tables
        if (in_array($tableName, ['users', 'projects', 'media', 'api_tokens'])) {
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM $tableName");
            echo "    Records: " . $count['count'] . "\n";
        }
    }
    
    // Test a simple query
    echo "\nTesting simple queries...\n";
    
    // Check users table
    $userCount = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    echo "✓ Active users: " . $userCount['count'] . "\n";
    
    // Check projects table
    $projectCount = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1");
    echo "✓ Active projects: " . $projectCount['count'] . "\n";
    
    // Check media table
    $mediaCount = $db->fetchOne("SELECT COUNT(*) as count FROM media WHERE is_active = 1");
    echo "✓ Active media files: " . $mediaCount['count'] . "\n";
    
    echo "\n✓ All database tests passed!\n";
    
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
    echo "Error details:\n";
    echo "  Host: localhost\n";
    echo "  Database: flori_construction\n";
    echo "  Username: root\n";
    echo "  Password: (empty)\n";
    
    // Try to connect without database name to see if MySQL is running
    try {
        $testPdo = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
        echo "✓ MySQL server is running\n";
        
        // Check if database exists
        $databases = $testPdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
        if (in_array('flori_construction', $databases)) {
            echo "✓ Database 'flori_construction' exists\n";
        } else {
            echo "✗ Database 'flori_construction' does not exist\n";
            echo "Available databases:\n";
            foreach ($databases as $dbName) {
                echo "  - $dbName\n";
            }
        }
    } catch (Exception $e2) {
        echo "✗ MySQL server connection failed: " . $e2->getMessage() . "\n";
        echo "Please make sure XAMPP MySQL is running\n";
    }
}
?>

<?php
/**
 * Debug projects.php directly
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'debug_errors.log');

echo "Testing projects.php directly...\n\n";

// Simulate the exact same environment as the API call
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['HTTP_HOST'] = 'localhost';
$_SERVER['REQUEST_URI'] = '/erdevwe/api/projects.php';

// Capture output
ob_start();

try {
    // Include the projects API file
    include 'api/projects.php';
} catch (Exception $e) {
    echo "Exception caught: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "Error caught: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

$output = ob_get_clean();

echo "Output from projects.php:\n";
echo $output . "\n";

// Check if error log was created
if (file_exists('debug_errors.log')) {
    echo "\nError log contents:\n";
    echo file_get_contents('debug_errors.log');
}
?>

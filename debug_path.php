<?php
/**
 * Debug path issues
 */

echo "Current working directory: " . getcwd() . "\n";
echo "__FILE__: " . __FILE__ . "\n";
echo "__DIR__: " . __DIR__ . "\n";
echo "dirname(__FILE__): " . dirname(__FILE__) . "\n";

echo "\nChecking paths:\n";
echo "Current dir: " . getcwd() . "\n";
echo "API dir: " . __DIR__ . "/api\n";
echo "Config dir: " . __DIR__ . "/config\n";

echo "\nChecking if files exist:\n";
echo "config/config.php exists: " . (file_exists("config/config.php") ? "YES" : "NO") . "\n";
echo "../config/config.php exists: " . (file_exists("../config/config.php") ? "YES" : "NO") . "\n";
echo "__DIR__/../config/config.php exists: " . (file_exists(__DIR__ . "/../config/config.php") ? "YES" : "NO") . "\n";
echo "dirname(__DIR__)/config/config.php exists: " . (file_exists(dirname(__DIR__) . "/config/config.php") ? "YES" : "NO") . "\n";

// Test from api directory perspective
echo "\nFrom API directory perspective:\n";
$apiDir = __DIR__ . "/api";
echo "API dir: $apiDir\n";
if (is_dir($apiDir)) {
    $oldCwd = getcwd();
    chdir($apiDir);
    echo "Changed to API dir, new cwd: " . getcwd() . "\n";
    echo "../config/config.php exists: " . (file_exists("../config/config.php") ? "YES" : "NO") . "\n";
    echo "Absolute path to config: " . realpath("../config/config.php") . "\n";
    chdir($oldCwd);
}
?>
